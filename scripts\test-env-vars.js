#!/usr/bin/env node

/**
 * 环境变量测试脚本
 * 用于验证Docker构建和运行时的环境变量配置
 */

console.log('🔍 环境变量检查报告');
console.log('='.repeat(50));

// 检查构建时环境变量（NEXT_PUBLIC_*）
const buildTimeVars = {
  'NEXT_PUBLIC_OPENAI_API_KEY': process.env.NEXT_PUBLIC_OPENAI_API_KEY,
  'NEXT_PUBLIC_FINNHUB_API_KEY': process.env.NEXT_PUBLIC_FINNHUB_API_KEY,
  'NEXT_PUBLIC_API_BASE_URL': process.env.NEXT_PUBLIC_API_BASE_URL,
  'NEXT_PUBLIC_WS_URL': process.env.NEXT_PUBLIC_WS_URL,
};

// 检查运行时环境变量
const runtimeVars = {
  'NODE_ENV': process.env.NODE_ENV,
  'PORT': process.env.PORT,
  'HOSTNAME': process.env.HOSTNAME,
  'DB_HOST': process.env.DB_HOST,
  'DB_PORT': process.env.DB_PORT,
  'DB_USER': process.env.DB_USER,
  'DB_PASSWORD': process.env.DB_PASSWORD,
  'DB_NAME': process.env.DB_NAME,
};

console.log('\n📦 构建时环境变量 (NEXT_PUBLIC_*)');
console.log('-'.repeat(30));
Object.entries(buildTimeVars).forEach(([key, value]) => {
  const status = value ? '✅' : '❌';
  const displayValue = value ? 
    (key.includes('KEY') ? `${value.substring(0, 10)}...` : value) : 
    'undefined';
  console.log(`${status} ${key}: ${displayValue}`);
});

console.log('\n🚀 运行时环境变量');
console.log('-'.repeat(30));
Object.entries(runtimeVars).forEach(([key, value]) => {
  const status = value ? '✅' : '❌';
  const displayValue = value ? 
    (key.includes('PASSWORD') ? '***' : value) : 
    'undefined';
  console.log(`${status} ${key}: ${displayValue}`);
});

// 检查关键配置
console.log('\n🔧 配置验证');
console.log('-'.repeat(30));

const checks = [
  {
    name: 'OpenAI API Key',
    test: () => buildTimeVars.NEXT_PUBLIC_OPENAI_API_KEY && 
                buildTimeVars.NEXT_PUBLIC_OPENAI_API_KEY.startsWith('sk-'),
    message: 'OpenAI API密钥格式正确'
  },
  {
    name: 'API Base URL',
    test: () => buildTimeVars.NEXT_PUBLIC_API_BASE_URL && 
                buildTimeVars.NEXT_PUBLIC_API_BASE_URL.startsWith('http'),
    message: 'API基础URL格式正确'
  },
  {
    name: 'WebSocket URL',
    test: () => buildTimeVars.NEXT_PUBLIC_WS_URL && 
                buildTimeVars.NEXT_PUBLIC_WS_URL.startsWith('ws'),
    message: 'WebSocket URL格式正确'
  },
  {
    name: 'Production Environment',
    test: () => runtimeVars.NODE_ENV === 'production',
    message: '生产环境配置正确'
  }
];

checks.forEach(check => {
  const status = check.test() ? '✅' : '❌';
  console.log(`${status} ${check.message}`);
});

// 生成摘要
const totalVars = Object.keys({...buildTimeVars, ...runtimeVars}).length;
const configuredVars = Object.values({...buildTimeVars, ...runtimeVars})
  .filter(value => value !== undefined && value !== '').length;
const passedChecks = checks.filter(check => check.test()).length;

console.log('\n📊 配置摘要');
console.log('-'.repeat(30));
console.log(`环境变量配置: ${configuredVars}/${totalVars}`);
console.log(`验证检查通过: ${passedChecks}/${checks.length}`);

if (configuredVars === totalVars && passedChecks === checks.length) {
  console.log('\n🎉 所有配置检查通过！');
  process.exit(0);
} else {
  console.log('\n⚠️  存在配置问题，请检查上述报告');
  process.exit(1);
}
