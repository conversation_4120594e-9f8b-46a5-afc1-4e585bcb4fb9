# Docker环境变量配置指南

## 📋 概述

本文档说明了如何配置Docker部署中的环境变量，特别是从GitHub Secrets获取NEXT_PUBLIC_OPENAI_API_KEY等敏感信息。

## 🔄 配置更改摘要

### 1. CI/CD配置更新 (`.github/workflows/ci.yml`)

**Docker构建阶段添加环境变量：**
```yaml
build-args: |
  NODE_ENV=production
  BUILD_DATE=${{ fromJSON(steps.meta.outputs.json).labels['org.opencontainers.image.created'] }}
  VCS_REF=${{ github.sha }}
  NEXT_PUBLIC_OPENAI_API_KEY=${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}
  NEXT_PUBLIC_FINNHUB_API_KEY=${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}
  NEXT_PUBLIC_API_BASE_URL=${{ env.API_URL }}
  NEXT_PUBLIC_WS_URL=${{ secrets.NEXT_PUBLIC_WS_URL }}
```

**镜像测试阶段添加环境变量验证：**
```yaml
docker run --rm -d --name test-container \
  -p 3000:3000 \
  -e NODE_ENV=production \
  -e NEXT_PUBLIC_OPENAI_API_KEY="${{ secrets.NEXT_PUBLIC_OPENAI_API_KEY }}" \
  -e NEXT_PUBLIC_FINNHUB_API_KEY="${{ secrets.NEXT_PUBLIC_FINNHUB_API_KEY }}" \
  "$IMAGE_TAG"
```

### 2. Dockerfile更新 (`docker/Dockerfile`)

**构建阶段添加ARG和ENV：**
```dockerfile
# 接受构建参数
ARG NODE_ENV=production
ARG NEXT_PUBLIC_OPENAI_API_KEY
ARG NEXT_PUBLIC_FINNHUB_API_KEY
ARG NEXT_PUBLIC_API_BASE_URL
ARG NEXT_PUBLIC_WS_URL
ARG BUILD_DATE
ARG VCS_REF

# 设置环境变量（Next.js构建时需要）
ENV NODE_ENV=$NODE_ENV
ENV NEXT_PUBLIC_OPENAI_API_KEY=$NEXT_PUBLIC_OPENAI_API_KEY
ENV NEXT_PUBLIC_FINNHUB_API_KEY=$NEXT_PUBLIC_FINNHUB_API_KEY
ENV NEXT_PUBLIC_API_BASE_URL=$NEXT_PUBLIC_API_BASE_URL
ENV NEXT_PUBLIC_WS_URL=$NEXT_PUBLIC_WS_URL
```

**运行阶段添加镜像标签：**
```dockerfile
# 添加镜像标签
LABEL org.opencontainers.image.title="TradingAgents Frontend" \
      org.opencontainers.image.description="多智能体大语言模型金融交易框架前端" \
      org.opencontainers.image.vendor="TradingAgents" \
      org.opencontainers.image.created=$BUILD_DATE \
      org.opencontainers.image.revision=$VCS_REF
```

### 3. 环境变量测试脚本 (`scripts/test-env-vars.js`)

新增测试脚本用于验证环境变量配置：
```bash
npm run test:env
```

## 🔑 必需的GitHub Secrets

### API密钥
- `NEXT_PUBLIC_OPENAI_API_KEY` - OpenAI API密钥
- `NEXT_PUBLIC_FINNHUB_API_KEY` - Finnhub API密钥

### 服务配置
- `NEXT_PUBLIC_WS_URL` - WebSocket服务URL
- `API_URL` - 后端API服务URL
- `DEPLOY_URL` - 部署后的应用访问URL

### 阿里云配置
- `ALIYUN_REGISTRY_USERNAME` - 阿里云容器镜像服务用户名
- `ALIYUN_REGISTRY_PASSWORD` - 阿里云容器镜像服务密码

### 部署配置
- `DEPLOY_HOST` - 部署服务器IP
- `DEPLOY_USER` - 部署用户名
- `DEPLOY_PATH` - 部署路径
- `DEPLOY_SSH_KEY` - SSH私钥

### 数据库配置
- `MYSQL_ROOT_PASSWORD` - MySQL root密码
- `MYSQL_PASSWORD` - MySQL应用用户密码

## 🛠️ 配置步骤

### 1. 设置GitHub Secrets

参考 `docs/GITHUB_SECRETS_SETUP.md` 文档进行详细配置。

### 2. 验证配置

推送代码到仓库后，GitHub Actions会自动：
1. 使用Secrets中的环境变量构建Docker镜像
2. 测试镜像启动和环境变量配置
3. 部署到目标服务器

### 3. 本地测试

```bash
# 测试环境变量配置
npm run test:env

# 本地Docker构建测试
docker build -f docker/Dockerfile \
  --build-arg NEXT_PUBLIC_OPENAI_API_KEY=sk-test... \
  --build-arg NEXT_PUBLIC_FINNHUB_API_KEY=test... \
  -t trading-agents-frontend:test .
```

## 🔍 环境变量作用域

### 构建时变量 (Build-time)
- `NEXT_PUBLIC_*` 变量在Next.js构建时被编译到静态文件中
- 这些变量在客户端代码中可见
- 必须在Docker构建阶段传入

### 运行时变量 (Runtime)
- 数据库连接信息等敏感配置
- 仅在服务器端可用
- 通过docker-compose.yml或容器启动时传入

## ⚠️ 安全注意事项

1. **敏感信息保护**
   - 永远不要在代码中硬编码API密钥
   - 使用GitHub Secrets管理敏感信息
   - 定期轮换API密钥

2. **环境隔离**
   - 开发、测试、生产环境使用不同的API密钥
   - 通过不同的GitHub环境管理不同的配置

3. **访问控制**
   - 限制GitHub Secrets的访问权限
   - 使用最小权限原则

## 🚨 故障排除

### 问题1：环境变量未传递
```bash
# 检查Docker构建日志
docker build --progress=plain ...

# 运行环境变量测试
npm run test:env
```

### 问题2：API密钥格式错误
```bash
# 验证OpenAI API密钥格式
echo $NEXT_PUBLIC_OPENAI_API_KEY | grep -E '^sk-[a-zA-Z0-9]{48}$'
```

### 问题3：构建失败
```bash
# 检查GitHub Actions日志
# 验证所有必需的Secrets是否已配置
```

## 📚 相关文档

- [GitHub Secrets完整配置指南](docs/GITHUB_SECRETS_SETUP.md)
- [GitHub Secrets检查指南](scripts/check-github-secrets.md)
- [Docker部署文档](docker/README.md)

---

**💡 提示：** 配置完成后，建议先在develop分支测试，确认环境变量正确传递后再合并到main分支。
