# GitHub Secrets 完整配置指南

## 📋 概述

本文档详细说明了如何在GitHub仓库中配置所有必需的Secrets，以支持CI/CD流程和Docker部署。

## 🔑 必需的Secrets列表

### 阿里云容器镜像服务
- `ALIYUN_REGISTRY_USERNAME` - 阿里云容器镜像服务用户名
- `ALIYUN_REGISTRY_PASSWORD` - 阿里云容器镜像服务密码

### API密钥配置
- `NEXT_PUBLIC_OPENAI_API_KEY` - OpenAI API密钥（用于AI功能）
- `NEXT_PUBLIC_FINNHUB_API_KEY` - Finnhub API密钥（用于股票数据）

### 服务URL配置
- `NEXT_PUBLIC_WS_URL` - WebSocket服务URL
- `API_URL` - 后端API服务URL
- `DEPLOY_URL` - 部署后的应用访问URL

### 部署服务器配置
- `DEPLOY_HOST` - 部署服务器IP地址
- `DEPLOY_USER` - 部署服务器用户名
- `DEPLOY_PATH` - 部署路径
- `DEPLOY_SSH_KEY` - SSH私钥

### 数据库配置
- `MYSQL_ROOT_PASSWORD` - MySQL root密码
- `MYSQL_PASSWORD` - MySQL应用用户密码

## 🛠️ 配置步骤

### 1. 访问GitHub Secrets设置

1. 进入您的GitHub仓库页面
2. 点击 `Settings` 选项卡
3. 在左侧菜单选择 `Secrets and variables` > `Actions`
4. 点击 `New repository secret` 开始添加

### 2. 阿里云容器镜像服务配置

**ALIYUN_REGISTRY_USERNAME:**
```
Name: ALIYUN_REGISTRY_USERNAME
Secret: aliyun1315382626
```

**ALIYUN_REGISTRY_PASSWORD:**
```
Name: ALIYUN_REGISTRY_PASSWORD
Secret: ezreal123
```

### 3. API密钥配置

**NEXT_PUBLIC_OPENAI_API_KEY:**
```
Name: NEXT_PUBLIC_OPENAI_API_KEY
Secret: sk-your-openai-api-key-here
```
> 📝 注意：请替换为您的实际OpenAI API密钥

**NEXT_PUBLIC_FINNHUB_API_KEY:**
```
Name: NEXT_PUBLIC_FINNHUB_API_KEY
Secret: your-finnhub-api-key-here
```
> 📝 注意：请替换为您的实际Finnhub API密钥

### 4. 服务URL配置

**NEXT_PUBLIC_WS_URL:**
```
Name: NEXT_PUBLIC_WS_URL
Secret: ws://your-domain:8000
```
> 📝 示例：`ws://*************:8000`

**API_URL:**
```
Name: API_URL
Secret: http://your-domain:5000
```
> 📝 示例：`http://*************:5000`

**DEPLOY_URL:**
```
Name: DEPLOY_URL
Secret: http://your-domain:3000
```
> 📝 示例：`http://*************:3000`

### 5. 部署服务器配置

**DEPLOY_HOST:**
```
Name: DEPLOY_HOST
Secret: your-server-ip
```
> 📝 示例：`*************`

**DEPLOY_USER:**
```
Name: DEPLOY_USER
Secret: root
```

**DEPLOY_PATH:**
```
Name: DEPLOY_PATH
Secret: /root
```

**DEPLOY_SSH_KEY:**
```
Name: DEPLOY_SSH_KEY
Secret: -----BEGIN OPENSSH PRIVATE KEY-----
your-private-ssh-key-content-here
-----END OPENSSH PRIVATE KEY-----
```
> 📝 注意：包含完整的SSH私钥内容，包括开始和结束标记

### 6. 数据库配置

**MYSQL_ROOT_PASSWORD:**
```
Name: MYSQL_ROOT_PASSWORD
Secret: trading123
```

**MYSQL_PASSWORD:**
```
Name: MYSQL_PASSWORD
Secret: trading123
```

## 🔍 验证配置

### 检查Secrets是否正确添加

在GitHub仓库的 `Settings` > `Secrets and variables` > `Actions` 页面，您应该看到所有上述Secrets都已列出。

### 测试CI/CD流程

1. 推送代码到main或develop分支
2. 查看GitHub Actions的运行结果
3. 确认Docker构建和部署步骤成功

## ⚠️ 重要注意事项

### 安全性
- 永远不要在代码中硬编码敏感信息
- 定期轮换API密钥和密码
- 限制SSH密钥的访问权限

### 环境变量的作用域
- `NEXT_PUBLIC_*` 变量会在构建时编译到前端代码中
- 其他变量仅在服务器端可用

### 构建时vs运行时
- OpenAI和Finnhub API密钥需要在Docker构建时传入
- 数据库密码等在容器运行时传入

## 🚨 常见问题

### 问题1：API密钥无效
```
Error: Invalid API key
```
**解决方案：**
- 检查API密钥格式是否正确
- 确认API密钥未过期
- 验证API密钥权限

### 问题2：Docker登录失败
```
Error: Username and password required
```
**解决方案：**
- 检查阿里云凭据是否正确
- 确认Secret名称大小写匹配
- 验证阿里云账号权限

### 问题3：部署连接失败
```
Error: Connection refused
```
**解决方案：**
- 检查服务器IP地址
- 验证SSH密钥格式
- 确认服务器防火墙设置

## 📞 获取帮助

如果遇到问题：
1. 检查GitHub Actions日志
2. 验证所有Secrets配置
3. 测试本地Docker构建
4. 联系项目维护者

---

**💡 提示：** 配置完成后，建议先在develop分支测试，确认无误后再合并到main分支进行生产部署。
